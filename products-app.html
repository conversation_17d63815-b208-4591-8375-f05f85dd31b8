<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏中心</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        /* 游戏中心页面特有样式 */
        .hero {
            padding: 140px 0 60px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 3.5rem;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 1.125rem;
            margin-bottom: 40px;
            max-width: 480px;
        }

        .main-content {
            margin: 40px 0 0 0;
        }

        /* 游戏网格布局 */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 32px;
            padding: 60px 0;
            position: relative;
            z-index: 1;
        }

        /* 游戏卡片样式 */
        .game-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .game-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 119, 48, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .game-image {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .game-card:hover .game-image img {
            transform: scale(1.05);
        }

        .game-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
            height: 100%;
        }

        .game-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            margin: 0;
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        .game-meta {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 8px;
        }

        .game-category {
            background: linear-gradient(135deg, #7877c6 0%, #ff7730 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-age {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.6;
            margin: 8px 0;
            flex: 1;
            font-weight: 400;
        }

        .game-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: auto;
            padding-top: 16px;
        }

        .download-btn {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 119, 48, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
        }

        .detail-btn {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: inline-block;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .detail-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        /* 游戏页面特有样式 */
        .main-content {
            padding: 60px 0;
        }

        /* 分类筛选 */
        .filter-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 32px;
            border-radius: 16px;
            margin-bottom: 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border-color: rgba(255, 119, 48, 0.3);
            transform: translateY(-1px);
        }

        /* 游戏网格布局 */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 32px;
            padding: 60px 0;
            position: relative;
            z-index: 1;
        }

        /* 游戏卡片样式 */
        .game-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .game-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 119, 48, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .game-image {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .game-card:hover .game-image img {
            transform: scale(1.05);
        }

        .game-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
            height: 100%;
        }

        .game-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            margin: 0;
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        .game-meta {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 8px;
        }

        .game-category {
            background: linear-gradient(135deg, #7877c6 0%, #ff7730 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-age {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.6;
            margin: 8px 0;
            flex: 1;
            font-weight: 400;
        }

        .game-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: auto;
            padding-top: 16px;
        }

        .download-btn {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 119, 48, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
        }

        .detail-btn {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: inline-block;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .detail-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        /* 分类筛选 */
        .filter-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 32px;
            border-radius: 16px;
            margin-bottom: 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border-color: rgba(255, 119, 48, 0.3);
            transform: translateY(-1px);
        }

        /* 页脚样式 */
        .footer {
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 60px 0 30px;
            margin-top: 80px;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 119, 48, 0.03) 0%, rgba(120, 119, 198, 0.03) 100%);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.125rem;
            margin-bottom: 20px;
            color: white;
            position: relative;
            padding-bottom: 12px;
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #ff7730, #7877c6);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 12px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: inline-block;
            font-weight: 400;
        }

        .footer-section ul li a:hover {
            color: #ff7730;
            transform: translateX(4px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: rgba(255, 255, 255, 0.7);
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(4px);
        }

        .contact-info i {
            margin-right: 12px;
            color: #ff7730;
            font-size: 1rem;
            width: 18px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 8px;
            font-size: 0.8rem;
            line-height: 1.5;
            font-weight: 400;
        }

        .footer-bottom a {
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            color: #ff7730;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 120px 0 50px;
            }

            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .games-grid {
                grid-template-columns: 1fr;
                gap: 24px;
                padding: 40px 0;
            }

            .game-card {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .game-image {
                width: 100%;
                height: 200px;
                align-self: center;
            }

            .filter-section {
                padding: 24px;
            }

            .filter-buttons {
                gap: 8px;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.8rem;
            }

            .nav-links {
                gap: 24px;
            }

            .nav-auth {
                gap: 12px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 8px 16px;
                font-size: 13px;
            }

            .footer {
                padding: 40px 0 20px;
            }

            .footer-content {
                gap: 32px;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2rem;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 16px;
                padding: 16px 0;
            }

            .nav-links {
                gap: 16px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-auth {
                gap: 8px;
            }

            .game-card {
                padding: 16px;
            }

            .filter-section {
                padding: 20px;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html" class="active">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏中心标题区域 -->
    <div class="hero">
        <div class="container">
            <h2>🎮 游戏中心</h2>
            <p>探索精彩游戏世界，发现你的下一个最爱</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">

            <!-- 游戏分类筛选 -->
            <!-- <section class="filter-section">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">游戏分类</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-category="all">全部游戏</button>
                    <button class="filter-btn" data-category="战略">战略策略</button>
                    <button class="filter-btn" data-category="角色扮演">角色扮演</button>
                    <button class="filter-btn" data-category="休闲">休闲益智</button>
                    <button class="filter-btn" data-category="动作">动作冒险</button>
                    <button class="filter-btn" data-category="竞技">竞技体育</button>
                </div>
            </section> -->

            <!-- 游戏列表 -->
            <section class="games-grid" id="gamesContainer">
                <!-- 游戏卡片将通过JavaScript动态生成 -->
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <!-- 脚本 -->
    <script src="js/login.js"></script>
    <script src="js/common.js"></script>
    <script src="js/games-data.js"></script>
    <script>
        // 动态生成游戏卡片
        function renderGames(games = gamesList) {
            const gamesContainer = document.getElementById('gamesContainer');
            gamesContainer.innerHTML = '';
            
            games.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                gameCard.innerHTML = `
                    <div class="game-image">
                        <img src="${game.image}" alt="${game.name}">
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">${game.name}</h3>
                        <div class="game-meta">
                            <span class="game-category">${game.category}</span>
                            <span class="game-age">${game.ageRating}</span>
                        </div>
                        <p class="game-description">${game.description}</p>
                        <div class="game-actions">
                            <a href="${game.downloadUrl}" class="download-btn">
                                <i class="fas fa-download"></i>
                                游戏下载
                            </a>
                            <a href="product-detail.html?id=${game.id}" class="detail-btn">
                                查看详情
                            </a>
                        </div>
                    </div>
                `;
                gamesContainer.appendChild(gameCard);
            });
        }

        // 分类筛选功能
        function initCategoryFilter() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 移除所有按钮的激活状态
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // 激活当前按钮
                    button.classList.add('active');
                    
                    const category = button.dataset.category;
                    
                    if (category === 'all') {
                        renderGames(gamesList);
                    } else {
                        const filteredGames = gamesList.filter(game => game.category === category);
                        renderGames(filteredGames);
                    }
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            renderGames();
            initCategoryFilter();
        });
    </script>
</body>
</html> 