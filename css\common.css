/* 公用样式 - 现代科技风格 */

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.7;
    color: #1a1a1a;
    background: #0a0a0a;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 动态背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

/* 复用样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 头部和导航 */
.header {
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
    color: white;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header.scrolled {
    background: rgba(10, 10, 10, 0.95);
    padding: 15px 0;
}

.nav {
    display: none;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    transition: all 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 32px;
}

.nav-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
}

.nav-links a::before {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff7730, #7877c6);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-links a:hover,
.nav-links a.active {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.nav-links a:hover::before,
.nav-links a.active::before {
    width: 100%;
}

/* 登录注册按钮样式 */
.nav-auth {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-auth a {
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.nav-auth .login-btn {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-auth .login-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-1px);
}

.nav-auth .register-btn {
    background: linear-gradient(135deg, #7877c6 0%, #ff7730 100%);
    color: white;
    border: 1px solid rgba(120, 119, 198, 0.3);
}

.nav-auth .register-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

/* 发布按钮样式 */
.publish-btn {
    background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%) !important;
    color: white !important;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 119, 48, 0.3);
}

.publish-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
}

.logout-btn {
    color: rgba(255, 255, 255, 0.8) !important;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    transform: translateY(-1px);
}

.auth-btns {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-btns {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-btns a {
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* 英雄区域基础样式 */
.hero {
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 800px;
    height: 800px;
    background: radial-gradient(circle, rgba(120, 119, 198, 0.1) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
}

.hero h2 {
    color: white;
    font-weight: 800;
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, #ffffff 0%, #7877c6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.hero p {
    color: rgba(255, 255, 255, 0.7);
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 1;
    font-weight: 400;
    line-height: 1.6;
}

/* 主要内容区域 */
.main-content {
    background: rgba(15, 15, 15, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    position: relative;
    z-index: 10;
    overflow: hidden;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.05) 0%, rgba(255, 119, 48, 0.05) 100%);
    pointer-events: none;
}

/* 按钮基础样式 */
.hero-button {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
}

.primary-button {
    background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
    color: white;
    box-shadow: 0 8px 32px rgba(255, 119, 48, 0.3);
    border: 1px solid rgba(255, 119, 48, 0.2);
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(255, 119, 48, 0.4);
}

.secondary-button {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.secondary-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(255, 255, 255, 0.1);
}

/* 页脚样式 */
.footer {
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    padding: 60px 0 30px;
    margin-top: 80px;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 119, 48, 0.03) 0%, rgba(120, 119, 198, 0.03) 100%);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    font-size: 1.125rem;
    margin-bottom: 20px;
    color: white;
    position: relative;
    padding-bottom: 12px;
    font-weight: 700;
    letter-spacing: -0.01em;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #ff7730, #7877c6);
    border-radius: 1px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 12px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: inline-block;
    font-weight: 400;
}

.footer-section ul li a:hover {
    color: #ff7730;
    transform: translateX(4px);
}

.contact-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.contact-info:hover {
    color: white;
    transform: translateX(4px);
}

.contact-info i {
    margin-right: 12px;
    color: #ff7730;
    font-size: 1rem;
    width: 18px;
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
    position: relative;
    z-index: 1;
}

.footer-bottom p {
    margin-bottom: 8px;
    font-size: 0.8rem;
    line-height: 1.5;
    font-weight: 400;
}

.footer-bottom a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s ease;
    text-decoration: none;
}

.footer-bottom a:hover {
    color: #ff7730;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .nav-links {
        gap: 24px;
    }

    .nav-auth {
        gap: 12px;
    }

    .nav-auth a,
    .user-btns a {
        padding: 8px 16px;
        font-size: 13px;
    }

    .footer {
        padding: 40px 0 20px;
    }

    .footer-content {
        gap: 32px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        flex-direction: column;
        height: auto;
        gap: 16px;
        padding: 16px 0;
    }

    .nav-links {
        gap: 16px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-auth {
        gap: 8px;
    }
}
