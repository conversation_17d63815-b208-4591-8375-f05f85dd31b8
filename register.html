<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        /* 注册页面特有样式 */
        .hero {
            padding: 140px 0 80px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 3rem;
            margin-bottom: 16px;
        }

        .hero p {
            font-size: 1.125rem;
            margin-bottom: 40px;
            max-width: 480px;
        }

        /* 注册表单样式 */
        .register-form {
            max-width: 450px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .register-title {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #7877c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.01em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 1rem;
            letter-spacing: 0.5px;
        }

        .form-group input {
            width: 100%;
            padding: 16px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.05);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-group input:focus {
            outline: none;
            border-color: #ff7730;
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(255, 119, 48, 0.1);
            transform: translateY(-1px);
        }
        /* 优化滑动验证码样式 */
        .slider-container {
            position: relative;
            background: rgba(255, 255, 255, 0.05);
            height: 50px;
            line-height: 50px;
            text-align: center;
            border-radius: 12px;
            margin-bottom: 25px;
            user-select: none;
            touch-action: none;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .slider-bg {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            border-radius: 11px;
            width: 0;
            transition: width 0.2s ease;
        }

        .slider-button {
            position: absolute;
            left: 2px;
            top: 50%;
            transform: translateY(-50%);
            width: 46px;
            height: 46px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            border-radius: 11px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 10;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 119, 48, 0.3);
        }

        .slider-button:hover {
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.4);
            transform: translateY(-50%) scale(1.05);
        }

        .slider-text {
            position: relative;
            z-index: 1;
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s;
            font-weight: 500;
            font-size: 14px;
        }

        .register-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
            letter-spacing: 0.5px;
        }

        .register-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(255, 119, 48, 0.4);
        }

        .register-links {
            margin-top: 30px;
            text-align: center;
        }

        .register-links a {
            color: #ff7730;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .register-links a:hover {
            color: #7877c6;
            text-decoration: underline;
        }

        .register-links span {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.95rem;
        }

        /* 添加禁用按钮样式 */
        .register-button:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 添加输入框错误状态样式 */
        .form-group input.error {
            border-color: #ff4757;
            box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
        }

        /* 错误提示样式 */
        #passwordError {
            color: #ff4757;
            font-size: 12px;
            display: none;
            margin-top: 5px;
        }

        /* 协议同意复选框样式 */
        .agreement-container {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 25px;
            padding: 0;
            transition: all 0.3s ease;
        }

        .agreement-container input[type="checkbox"] {
            margin: 0;
            transform: scale(1.3);
            accent-color: #ff7730;
            cursor: pointer;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .agreement-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
            cursor: pointer;
            flex: 1;
        }

        .agreement-text a {
            color: #ff7730;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 1px solid transparent;
        }

        .agreement-text a:hover {
            color: #7877c6;
            border-bottom-color: #7877c6;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: rgba(15, 15, 15, 0.95);
            backdrop-filter: blur(20px);
            margin: 5% auto;
            padding: 30px;
            border-radius: 16px;
            width: 100%;
            max-width: 888px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-header h3 {
            color: white;
            font-size: 1.5em;
            margin: 0;
            font-weight: 700;
        }

        .close {
            color: rgba(255, 255, 255, 0.6);
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ff7730;
        }

        .modal-body {
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
        }

        .modal-body h4 {
            color: #ff7730;
            margin: 20px 0 10px 0;
        }

        .modal-body h3 {
            color: white;
            margin: 20px 0 10px 0;
        }

        .modal-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .modal-btn {
            padding: 12px 30px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
        }

        .modal-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 120px 0 60px;
            }

            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .register-form {
                padding: 0 16px;
            }

            .register-title {
                font-size: 1.8rem;
            }

            .modal-content {
                margin: 2% auto;
                padding: 20px;
                max-width: 95%;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2rem;
            }

            .register-title {
                font-size: 1.6rem;
            }

            .register-form {
                padding: 0 12px;
            }

            .modal-content {
                padding: 16px;
            }
        }

    </style>
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="hero">
        <div class="container">
            <h2>用户注册</h2>
            <p>创建您的账户，开启精彩游戏之旅</p>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="register-form">
                <!-- <h2 class="register-title">创建账户</h2> -->
            <form id="registerForm">
                <div class="form-group">
                    <label for="phone">手机号</label>
                    <input
                        type="tel"
                        id="phone"
                        name="phone"
                        required
                        pattern="^1[3-9]\d{9}$"
                        placeholder="请输入手机号"
                        oninvalid="this.setCustomValidity('请输入正确的11位手机号码')"
                        oninput="this.setCustomValidity('')">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        minlength="6"
                        placeholder="请输入密码"
                        oninvalid="this.setCustomValidity('密码长度至少为6位')"
                        oninput="this.setCustomValidity(''); validatePassword()">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        required
                        placeholder="请再次输入密码"
                        oninvalid="this.setCustomValidity('请再次输入密码')"
                        oninput="this.setCustomValidity(''); validatePassword()">
                    <span id="passwordError">两次输入的密码不一致</span>
                </div>

                <div class="slider-container">
                    <div class="slider-bg"></div>
                    <div class="slider-button">
                        <i class="fas fa-arrows-alt-h"></i>
                    </div>
                    <div class="slider-text">请向右滑动验证</div>
                </div>
                <div class="agreement-container">
                    <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                    <label for="agreeTerms" class="agreement-text">
                        我已阅读并同意 <a href="#" onclick="showTermsModal(); return false;">《账号注册协议》</a>
                    </label>
                </div>
                <button type="submit" class="register-button">注册</button>
                <div class="register-links">
                    <span>已有账号？</span>
                    <a href="login.html">立即登录</a>
                </div>
            </form>
            </div>
        </div>
    </div>

    <!-- 注册协议模态框 -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>用户注册及保密协议</h3>
                <span class="close" onclick="closeTermsModal()">&times;</span>
            </div>
            <div class="modal-body">
<div class="yonghu contentp">
            <p>特别提示</p>
            <p>本协议包括《用户注册服务协议》和《用户信息保密协议》，提醒用户认真阅读。</p>
            <h3>一、用户注册服务协议</h3>
            <p>广州立早网络科技有限公司（以下简称“立早网络”）旗下产品是指立早网络按照本协议的规定及其不时发布的操作规则提供的基于互联网的相关服务(以下称“网络服务”)。为获得网络服务，服务使用人(以下称“用户”)及其监护人同意本协议的全部条款并按照页面上的提示完成全部的注册程序。用户在监护人的陪同下进行注册程序过程中点击“同意”按钮,或者用户实际接受了立早网络的产品和服务，即表示用户完全接受本协议项下的全部条款。这些条款可由立早网络随时更新，本服务协议一旦发生变动，立早网络将会在相关的页面上提示修改内容。修改后的服务协议一旦在页面上公布即有效代替原来的服务协议。用户可随时查阅最新服务协议，如不接受，可停止使用立早网络提供的服务。用户在使用立早网络提供的服务之前，应仔细阅读本服务协议，如用户不同意本服务协议，请停止使用立早网络提供的服务。</p>
            <p>1. 账号服务条款的接受</p>
            <p>1.1 账号由立早网络、及其关联公司所有并合法运营，立早网络及其关联公司并在本用户服务协议的条款和要求下提供服务。 </p>
            <p>1.2 当用户访问、浏览及使用账号提供的各项服务、业务时，用户便表明其接受了本服务协议的条款，并同意受本服务协议的约束，用户并保证其提交的信息真实、准确、及时和完整；若用户不同意本协议条款的，请停止注册程序。</p>
            <p>1.3 本服务协议所称的用户是指完全同意本服务协议所有条款（以下简称“服务条款”）并访问或浏览账号相关页面的服务接受者。</p>
            <p>2. 服务条款的变更和修改</p>
            <p>立早网络保留依其自主判断在将来的任何时间变更、修改、增加或删除本服务协议条款的权利。所有修改的协议均构成本服务协议的一部分。立早网络有权随时对服务条款进行修改，一旦发生服务条款的变动，立早网络将公示修改的内容；当用户使用账号的任何服务时，应接受账号随时提供的与该服务相关的规则或说明，并且此规则或说明均构成本服务条款的一部分。用户如果不同意服务条款的修改，可以主动取消已经获得的服务；如果用户继续享用服务，则视为用户已经接受服务条款的修改。</p>
            <p>3. 服务说明</p>
            <p>3.1 立早网络运用自己的操作系统通过互联网向用户提供游戏服务（以下简称本服务）。除非另有明确规定，基于增强或强化目前服务目的所新增的任何新功能、服务、新产品，均无条件地适用本服务条款。 </p>
            <p>3.2 除非本协议中另有规定，否则立早网络对服务不承担任何责任，即用户对服务的使用承担风险。立早网络不保证服务一定会满足用户的使用要求，也不保证服务不会中断，对服务的及时性、安全性、准确性也不作担保。 </p>
            <p>3.3 为使用本服务，用户必须自行配备接入互联网所必需的设备，包括计算机、手机、掌上电脑及其他存取装置或接受服务所需其它设备，并自行支付与此服务有关的费用。 </p>
            <p>3.4 立早网络保留在日后对全部或部分服务项目收取费用的权利。 </p>
            <p>3.5 用户接受本服务时，须提供完整、真实、准确、最新的个人资料并按其变更情况不断更新账号资料。 </p>
            <p>3.6 若用户提供任何错误、不实、过时或不完整的资料，或者立早网络有合理理由怀疑前述资料为错误、不实、过时或不完整，立早网络有权暂停或终止用户的账号，并拒绝其现在或将来使用本服务的全部或一部分。 </p>
            <p>3.7 立早网络有权规定并修改使用本服务的一般措施，包括但不限于决定保留电子邮件信息或其他上传内容的时间、限制本服务一个账号可接收信息的数量等措施。如账号未能储存或删除本服务的内容或其他信息，立早网络不负担任何责任。对于用户3个月未使用的账号，立早网络有权（但无义务）予以删除。 </p>
            <p>3.8 由于用户经由本服务张贴、上传或传送内容、与本服务连线、违反本服务条款或侵害其他人的任何权利导致任何第三方提出权利主张或使立早网络遭受任何形式的罚款或处罚，用户同意以适当方式充分消除对立早网络的不利影响，赔偿立早网络及其分公司、关联公司、代理人或其他合作伙伴及员工的损失，并使其免受损害。</p>
            <p>4. 用户应遵守以下法律及法规</p>
            <p>用户同意遵守中华人民共和国相关法律、法规的规定，在使用账号时，不得侵犯任何第三方的合法权益。在任何情况下，如果立早网络有合理理由认为用户的行为可能违反上述法律、法规或侵犯第三方的合法权益，立早网络可以在任何时候，不经事先通知终止向该用户提供服务。用户应了解互联网的无国界性，应特别注意遵守当地所有有关的法律和法规。</p>
            <p>5. 用户隐私权</p>
            <p>用户在遵守上述协议要求的前提下，立早网络将充分保护用户享有个人隐私。除非因以下原因，未经用户授权，本网站不公开、编辑或透露其个人账号资料：  根据有关法律法规的要求； </p>
            <p>按照相关政府主管部门的要求； </p>
            <p>维护社会个体和公众的安全； </p>
            <p>为维护社会公共利益的需要；</p>
            <p>维护本网站的合法权益； </p>
            <p>事先获得用户的明确授权； </p>
            <p>符合其他相关的要求。</p>
            <p>6. 用户使用须知、用户账号、密码和安全</p>
            <p>6.1 保护用户（特别是未成年人）的隐私是立早网络的一项基本政策，因此，若父母（监护人）希望未成年人（尤其是八岁以下子女）得以使用本服务，未成年用户必须在父母（监护人）的陪同下申请注册，在接受本服务时，应以法定监护人身份加以判断本服务是否符合于未成年人。为加强监护人对未成年人参与网络游戏的监护，引导未成年人健康、绿色参与网络游戏，构建和谐家庭关系，立早网络在其官网设置"家长监护"机制，监护人可通过家长监护机制联系立早网络，纠正未成年子女沉迷游戏。 </p>
            <p>6.2立早网络要求用户提供与其个人身份有关的信息资料时，应当事先以明确而易见的方式向用户公开其隐私权保护政策和个人信息利用政策，并采取必要措施保护用户的个人信息资料的安全。</p>
            <p>6.3 用户一旦注册成功，便成为立早网络的注册用户，将得到一个密码和账号。用户有义务保证密码和账号的安全。用户对利用该密码和账号所进行的一切活动负全部责任；因此所衍生的任何损失或损害，立早网络不会承担任何责任。 </p>
            <p>6.2立早网络要求用户提供与其个人身份有关的信息资料时，应当事先以明确而易见的方式向用户公开其隐私权保护政策和个人信息利用政策，并采取必要措施保护用户的个人信息资料的安全。 </p>
            <p>6.3 用户一旦注册成功，便成为立早网络的注册用户，将得到一个密码和账号。用户有义务保证密码和账号的安全。用户对利用该密码和账号所进行的一切活动负全部责任；因此所衍生的任何损失或损害，立早网络不会承担任何责任。 </p>
            <p>6.4 用户的密码和账号遭到未授权的使用或发生其他任何安全问题，用户可以立即通知立早网络。用户在每次连线结束，应结束账号使用，否则用户可能得不到账号的安全保护。</p>
            <p>6.5 立早网络禁止用户交易账号，立早网络有权收回用户交易的账号。如果因用户交易账号引起的任何纠纷，立早网络不承担任何责任。</p>
            <p>6.6用户承诺以其真实身份注册成为立早网络旗下产品的用户，并保证所提供的个人信息真实、完整、有效。 </p>
            <p>6.7用户以真实身份注册成为立早网络旗下产品的用户后，需要修改所提供的个人身份资料信息的，应当及时通过立早网络公布的方式，修改保存在立早网络旗下产品的 个人身份资料信息。立早网络将及时、有效地为用户提供相关服务。  </p>
            <p>6.8立早网络有权审查用户注册所提供的身份信息是否真实、有效，并将采取技术与管理等合理措施保障用户账号的安全、有效；用户有义务妥善保管其账号及密码，并正确、安全地使用其账号及密码。任何一方未尽上述义务导致账号密码遗失、账号被盗等情形，并给用户和他人的民事权利造成损害的，应当承担由此产生的法律责任。 </p>
            <p>6.9用户对登录后所持有账号产生的行为依法享有权利和承担责任。 </p>
            <p>6.10用户发现其账号或密码被他人非法使用或有使用异常的情况的，应及时根据立早网络公布的处理方式通知立早网络，并有权通知立早网络采取措施暂停该账号的登录和使用。 </p>
            <p>6.11立早网络根据用户的通知采取措施暂停用户账号的登录和使用的，立早网络应当要求用户提供并核实与其注册身份信息相一致的个人有效身份信息。</p>
            <p>6.11.1立早网络核实用户所提供的个人有效身份信息与所注册的身份信息相一致的，将及时采取措施暂停用户账号的登录和使用。  </p>
            <p>6.11.2立早网络违反约定，未及时采取措施暂停用户账号的登录和使用，因此给用户造成损失的，将承担相应的法律责任。 </p>
            <p>6.11.3用户没有提供其个人有效身份证件或者用户提供的个人有效身份证件与所注册的身份信息不一致的，立早网络有权拒绝用户的上述请求。 </p>
            <p>6.12用户为了维护其合法权益，向立早网络提供与所注册的身份信息相一致的个人有效身份信息时，立早网络将为用户提供账号注册人证明、原始注册信息等必要的协助和支持，并根据需要向有关行政机关和司法机关提供相关证据信息资料。</p>
            <p>7. 对用户信息的存储和限制 </p>
            <p>立早网络不对用户在账号任何服务下发布信息的删除或储存失败负责。立早网络有权判断用户的行为是否符合账号服务条款的规定，如果立早网络认为用户违反了服务条款的规定，立早网络有权删除用户发布或发送的信息，直至中断或终止向其提供服务。 </p>
            <p>8. 禁止用户从事以下行为： </p>
            <p>8.1 上传、张贴、发送或传送任何非法、淫秽、色情、低俗的，胁迫、骚扰、中伤他人的，诽谤、侵害他人隐私或诋毁他人名誉或商誉的，其他违反国家法律法规、社会主义道德规范及不适当的信息或电子邮件，包括但不限于资讯、资料、文字、软件、音乐、照片、图形、信息、视频或其他资料（以下简称内容）。</p>
            <p>8.2 以任何方式危害未成年人。 </p>
            <p>8.3 冒充任何人或机构，或以虚伪不实的方式谎称或使人误认为与任何人或任何机构有关。 </p>
            <p>8.4 伪造标题或以其他方式操控识别资料，使人误认为该内容为立早网络所传送。 </p>
            <p>8.5 上传、张贴、发送电子邮件或以其他方式传送无权传送的内容。 </p>
            <p>8.6 上传、张贴、发送电子邮件或以其他方式传送侵犯任何人的专利、商标、著作权、商业秘密或其他民事权利之内容。 </p>
            <p>8.7 上传、张贴、发送电子邮件或以其他方式传送广告函件、促销资料、垃圾邮件等。 </p>
            <p>8.8 干扰或破坏本服务或与本服务相连的服务器和网络，或不遵守本服务协议之规定。</p>
            <p>8.9 违反任何相关的中国法律、法规、规章、条例等其他具有法律约束力的规范。</p>
            <p>8.10 跟踪或以其他方式骚扰他人。 </p>
            <p>8.11 窃取他人密码、账号和其它数字化信息或财产。 </p>
            <p>8.12 其他被立早网络视为不适当的行为。 </p>
            <p>8.13 立早网络有权对用户载、张贴、发送的内容进行审核，有任何违反法律法规或本协议之有关规定的内容，立早网络有权立即将其删除或屏蔽，且不需要对用户另行通知。</p>
            <p>8.14 若用户违反本用户守则的规定，用户可能遭受以下一项或几项惩罚： </p>
            <p>8.14.1 警告：警告仅仅是针对轻微违反用户注册服务协议而做出的教育导向，它是用于正常管理游戏运行的一种方式。</p>
            <p>8.14.2 禁言：关闭违规用户的部分或全部聊天频道，强制暂停用户角色的线上对话功能，使用户角色无法与其他用户对话，直到此次处罚到期或是取消。</p>
            <p>8.14.3 强制离线：强制让违规用户离开当前游戏，结束用户当前游戏程序的执行。　</p>
            <p> 　  8.14.4 封停账号：暂停或长期终止用户账号登录某款游戏的权利。</p>
            <p>8.14.5 删除档案：将用户在某个游戏世界中的人物档案删除，不让该人物再出现在游戏世界。</p>
            <p>8.14.6 删除账号：长期终止用户账号登陆游戏的权利，包括但不限于用户注册信息、角色信息、等级物品、游戏货币等游戏数据库中的全部数据都将被长期封禁。</p>
            <p>8.14.7收回游戏虚拟物品：对于用户因欺诈或其他违规行为而获取的游戏虚拟物品，包括但不限于游戏虚拟物品进行收回。</p>
            <p>8.14.8修改昵称：对于用户游戏人物或帮派等的命名强制修改。 </p>
            <p>8.14.9 解散组织：解散用户成立的帮派、公会等组织。</p>
            <p>8.14.10倒扣数值：针对游戏角色游戏数值进行扣除，包括但不限于游戏角色等级、金钱、经验等。</p>
            <p>8.14.11 封禁IP：暂时或长期禁止用户在某一异常IP登录某款游戏的某个服务器。</p>
            <p>8.14.12 承担法律责任：对于用户的不当行为对他人或者立早网络游戏平台造成损害或者与现行法律规定相违背的，违规用户要依法承担相应的民事、行政或刑事责任。</p>
            <p> 8.15账号封号规则</p>
            <p>8.15.1无视国家规定，在姓名、游戏角色名等地方中使用违规词语或不文明词语、非法字符  处罚规则：视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚。 </p>
            <p>8.15.2 涉及到侵入、拦截、破坏、复制、修改游戏程序，以及宣扬、叫卖和使用各种辅助性程序或恶性非法程序，即使用外挂程序、或在游戏中宣传外挂程序的行为。  处罚规则：视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚。 </p>
            <p>8.15.3以任何弄虚作假的形式来蒙蔽或者欺骗其他用户，如发布模仿官方并带有病毒的网站、非官方中奖信息、非法广告、游戏代码、木马、外挂、病毒、色情信息、垃圾广告等信息  处罚规则：视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚。 </p>
            <p>8.15.4 通过账号发布非法网站，宣传或使用私服、游戏代码、木马、外挂、病毒、色情信息、垃圾广告、非法广告等信息  处罚规则：视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚。</p>
            <p>8.15.5 通过账号发布触犯政府法令的文字如：煽动、反动、猥亵、暴力、种族歧视、宗教歧视等字眼，包含这类相关字的反向书写。  处罚规则：视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚，并上报相关部门追究其法律责任。</p>
            <p>8.15.6 宣传或贩卖BUG、攻击服务器运行、牟取个人利益、影响游戏公平性，以及影响其他用户正常进行游戏等行为  处罚规则：查证核实后，将暂停账号，并清除所有非法物品；视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚。 </p>
            <p>8.15.7 盗取或参与盗取他人账号，给被盗者造成严重损失的行为  处罚规则：查证核实后，视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚，并上报相关部门追究其法律责任。</p>
            <p>8.15.8侮辱、毁谤、猥亵、威胁、辱骂其他用户，扭曲事实、恶意散布不实谣言，恶意影响游戏环境等行为  处罚规则：视情节轻重给予时限性冻结账号的处罚；情节严重者，给予永久冻结的处罚。 </p>
            <p>8.15.9 线下交易或第三方平台购买虚拟货币、游戏道具等行为  处罚规则：立早网络将对异常虚拟货币、游戏道具进行查证，查证过程将暂时冻结账号。如发现来源违规将对涉事账号进行相关处理，包括但不限于封停账号、回收异常道具、扣除充值元宝等方式。由此造成的损失立早网络不承担任何责任。 </p>
            <p>8.16 如果立早网络发现用户数据异常或存在违规情况，立早网络有权根据本协议的规定，采取相应措施：包括但不限于对游戏帐号的冻结、封停、终止直至删除，以及对涉及使用外挂的游戏角色的封停和删除；对涉及使用外挂的游戏角色拥有的虚拟物品进行冻结或收回。基于上述行为而被立早网络冻结、终止、删除帐号、游戏角色、虚拟物品或采取其他限制措施的用户，无权因此要求立早网络承担任何责任。</p>
            <p>8.17 除非获得立早网络的书面许可，否则，用户不得利用立早网络的任何内容牟取商业利益，包括但不限于充当游戏道具交易中介收取中介费，以营利为目的销售游戏道具等；否则，立早网络有权同时采取以下措施：注销账号、删除档案等处理；情节严重的，立早网络保留追究用户法律责任的权利。 </p>
            <p>9. 内容及其披露 </p>
            <p>9.1 用户对经由本服务上传、张贴或传送的内容负全部责任；对于经由本服务而传送的内容，立早网络不保证前述内容的正确性、完整性或及时性。在任何情况下，立早网络均不对任何用户提供的内容负责，包括但不限于任何内容发生任何错误或纰漏以及衍生的任何损失或损害，用户负责处理与其提供的内容相关的任何及全部纠纷。立早网络有权（但无义务）拒绝或删除经由本服务提供的任何内容。用户使用上述内容，应自行承担风险。</p>
            <p>9.2 立早网络有权利在下述情况下，对内容进行保存或披露：</p>
            <p>法律程序所规定； </p>
            <p>本服务条款规定；  被侵害的第三人提出权利主张； </p>
            <p>为保护账号、其使用者及社会公众的权利、财产或人身安全； </p>
            <p>司法机关或行政机关基于法定程序要求立早网络提供的；</p>
            <p>立早网络为了维护自己合法权益而向用户提起诉讼或者仲裁时应用户监护人的合法要求而提供用户个人身份信息时其他立早网络认为有必要的情况。</p>
            <p>1.电子公告栏的张贴内容</p>
            <p>1.“电子公告栏”包括空间、论坛、SNS社区和其它一般公众可以使用的区域；</p>
            <p>2.用户一旦在本服务电子公告栏上传或张贴内容，即视为用户授予立早网络该内容著作权之免费及非独家、永久的许可使用权，立早网络有权为展示、传播及推广前述内容之目的，对上述内容进行复制、修改、出版。由此展示、传播及推广行为所产生的损失或利润，均由立早网络承担或享受。立早网络有权自主决定是否给予此类用户鼓励或奖励。</p>
            <p>3.因用户进行上述张贴，而导致任何第三方提出索赔要求或衍生的任何损害或损失，用户须承担全部责任。</p>
            <p>4.非经立早网络事先许可，用户不得对他人上传或张贴在电子公告栏或本服务其他内容进行复制、出售或用作其他商业用途。 </p>
            <p>11. 账号服务使用规则 </p>
            <p>11.1 用户必须保证，拥有上传之照片、文字等作品之著作权或已获得合法授权，在本网站之上传行为未侵犯任何第三方之合法权益。否则，将由用户承担由此带来的一切法律责任；用户不得将任何内部资料、机密资料、涉及他人隐私资料或侵犯任何人的专利、商标、著作权、商业秘密或其他专属权利之内容加以上传、张贴、或以其他方式传送。</p>
            <p>11.2 用户不得利用本服务进行故意制作、传播计算机病毒等破坏性程序，不得针对本服务、与本服务连接的服务器或网络制造干扰、混乱，或违反连接本服务的网络的任何要求、程序、政策或规则，否则立早网络将保留追究其法律责任的权利并有权将其提交给相关部门处理。</p>
            <p>11.3 立早网络有权对用户上传的图片、添加的文字等内容进行审核，有任何违反法律法规或本协议之有关规定的图片、文字，立早网络有权立即将其删除或屏蔽，且不需要对用户另行通知。 </p>
            <p>11.4 用户不得将广告函件、促销资料、垃圾邮件等，加以上传、张贴、发送电子邮件或以其他方式传送。</p>
            <p>11.5 立早网络郑重提请用户注意，任何经由本服务以上传、张贴、发送的资料、文字、照片、图形、视讯、信息、用户的登记资料或其他资料（以下简称“内容”），无论系公开还是私下传送，均由内容提供者承担责任。立早网络无法控制经由本服务传送之内容，也无法对用户的使用行为进行全面控制，因此不保证内容的合法性、正确性、完整性、真实性或品质；用户已预知使用本服务时，可能会接触到令人不快、不适当或令人厌恶之内容，并同意将自行加以判断并承担所有风险，而不依赖于立早网络。但在任何情况下，立早网络有权依法停止传输任何前述内容并采取相应行动，包括但不限于暂停用户使用本服务的全部或部分，保存有关记录，并向有关机关报告。但立早网络有权(但无义务)依其自行之考量，拒绝和删除可经由本服务提供之违反本条款的或其他引起立早网络或用户反感的任何内容。</p>
            <p>11.6 用户完全理解并同意，若第三方在用户不知情或未经用户同意的前提下，将其文字、图片作品上传于账号空间及由此所产生的任何可能侵害其权益的行为，立早网络均不对任何人承担任何责任。</p>
            <p>11.7 立早网络有权根据政府机关或权利人的要求删除用户上传、张贴、发送的内容，无论立早网络事先是否通知。</p>
            <p>11.8 立早网络有权根据服务的需要，收回用户所用的空间域名或减少用户所用空间的大小，无论立早网络事先是否通知。 </p>
            <p>12. 论坛规则 </p>
            <p>12.1 关于用户名和昵称 </p>
            <p>不得使用党和国家领导人或其他知名人士的真实姓名、字号、艺名、笔名作为用户名和昵称； </p>
            <p>不得使用国家机构或其他机构的名称作为用户名和昵称； </p>
            <p>不得使用和其他网友之名相近、相仿的用户名和昵称；</p>
            <p>不得使用不文明、不健康，或带攻击性、侮辱性的用户名和昵称；</p>
            <p>请勿使用易产生歧义、引起他人误解的用户名和昵称；</p>
            <p>不得使用各种奇形怪状的符号作为用户名和昵称；</p>
            <p>对于违反规定或产生不良后果的用户名和昵称，本站有权删除而不必事先通知。</p>
            <p>12.2 关于签名 </p>
            <p>不得出现宣扬反动、封建迷信、淫秽、色情、赌博、暴力、凶杀、恐怖、教唆犯罪等不符合国家法律规定的以及任何包含种族、性别、宗教歧视性和猥亵性的信息内容；</p>
            <p>不得出现有侮辱性言语、挑衅、辱骂其他人以及不健康内容；</p>
            <p>不得出现广告性质的内容以及立早网络以外其他网站的链接；</p>
            <p>立早网络有权在不需要通知和解释的情况下除去违反以上规定的签名内容,并对情节严重者予以封号处理。</p>
            <p>12.3 关于发贴内容 </p>
            <p>遵守相关法规，严禁发表违反法律法规及社会主义道德规定的内容； </p>
            <p>使用文明用语，不得张贴对任何人进行人身攻击、谩骂、诋毁的言论；</p>
            <p>不得张贴未经公开报道、未经证实的消息； </p>
            <p>不得张贴与所在论坛主题无关的消息、言论和图片； </p>
            <p>未经立早网络同意，不得张贴带有商业性质的内容或任何形式的广告，不得张贴立早网络及其关联公司以外其他网站的链接；</p>
            <p>不得恶意灌水，内容相同的帖子不得一文多发； </p>
            <p>不得在帖子中（标题和内容）加入各种奇形怪状的符号；</p>
            <p>转贴文章应注明原始出处和时间；</p>
            <p>对于违反以上规定的内容，在不需要通知和解释的情况下，立早网络以及版主有权予以删除，并对情节严重者予以封账号处理。 </p>
            <p>12.4 关于贴图 </p>
            <p>不得张贴宣扬反动、封建迷信、淫秽、色情、赌博、暴力、凶杀、恐怖、教唆犯罪等不符合国家法律规定的以及任何包含种族、性别、宗教歧视性和猥亵性的图片； </p>
            <p>不得出现带有侮辱性、挑衅、辱骂其他人以及不健康内容的图片； </p>
            <p>请勿使用易产生歧义、引起他人误解的图片； </p>
            <p>摘录、转贴的图片请注明出处及作者，禁止张贴侵犯他人著作权、版权等知识产权的图片；</p>
            <p>不得张贴与论坛主题无关的图片； </p>
            <p>立早网络有权在不需要通知和解释的情况下删除违反以上规定的图片，并对情节严重者予以封账号处理。 </p>
            <p>13. 账号游戏规则 </p>
            <p>13.1 用户应当遵守游戏公约、用户守则及不时发布的游戏规则、公告等； </p>
            <p>13.2 用户不得通过不正当的手段或其他不公平的手段使用账号的游戏产品和服务或参与账号的游戏活动。用户不得干扰立早网络正常地提供产品和服务，包括但不限于：攻击、侵入立早网络的网站服务器或使网站服务器过载；制作、发布、传播、使用任何形式的妨碍游戏公平性的辅助工具或程序(外挂)；利用程序的漏洞和错误(Bug)破坏游戏的正常进行或传播该漏洞或错误(Bug)；不合理地干扰或阻碍他人使用立早网络所提供的产品和服务。</p>
            <p>13.3 为维护游戏的公平性或平衡性，如果立早网络发现用户数据异常，无论用户对该异常数据产生是否负有过错，立早网络均有权根据本规则、游戏公约、用户守则及后期不时发布的游戏公告等，采取相应措施：包括但不限于对该账号的冻结、终止、删除；用户在此承诺立早网络有权采取上述行动，并承诺不得就上述行为要求立早网络做任何补偿或退费。 </p>
            <p>13.4 立早网络在此特别提示用户： </p>
            <p>立早网络提供的游戏将按照现状提供，用户明确知道游戏中存在已知和未知的漏洞和错误（Bug）。尽管立早网络将尽最大努力解决已知的漏洞和错误（Bug），但用户知道或应该知道游戏中仍可能包含未知的漏洞和错误（Bug）等。用户同意不向立早网络及其关联公司因任何漏洞和错误（Bug）主张任何赔偿。立早网络不提倡虚拟物品之间的交易，并不对该交易中产生的任何问题进行支持和保障。 </p>
            <p>14. 手机特色功能说明 </p>
            <p>14.1 用户明确知道，立早网络提供的实时定位服务将向用户及用户的不特定账号好友提供用户的实时空间位置信息。 </p>
            <p>14.2 用户可选择向账号导入手机通讯簿上所对应的好友关系链，但立早网络确保将不导入好友关系链之外的任何信息（包括好友姓名及其他任何相关信息） </p>
            <p>14.3 立早网络将对好友关系链通过不可逆转的方式加密存储，包括账号和您的任何好友都将无法获知您的真实好友关系链和真实手机号码。 </p>
            <p>15. 立早网络的知识产权及其他权利</p>
            <p>15.1 立早网络对本服务及本服务所使用的软件和受知识产权相关法律或其他法律保护的资料享有相应的权利。 </p>
            <p>15.2 经由本服务传送的内容，受到著作权法、商标法、专利法或其他法律的保护；未经立早网络明示授权许可，用户不得进行修改、出租、散布或衍生其他作品，用户本人创作并在公开使用区域张贴的内容除外。 </p>
            <p>15.3 用户对本服务所使用的软件有非专属性使用权，但自己不得或许可任何第三方复制、修改、出售或衍生产品。</p>
            <p>15.4 账号及其他账号图样、产品及服务名称，均为立早网络及其关联公司所享有的商标，未经立早网络所事先书面授权，任何人不得使用、 复制或用作其他用途。 </p>
            <p>16. 免责声明 </p>
            <p>不保证其正确性或可靠性；并且对于用户经本服务上的广告、展示而购买、取得的任何产品、信息或资料，立早网络不负保证责任。用户自行承担担使用本服务的风险。 </p>
            <p>16.2 立早网络有权但无义务，改善或更正本服务任何部分之任何疏漏、错误。 </p>
            <p>16.3 立早网络不保证以下事项（包括但不限于）： </p>
            <p>本服务适合用户的使用要求； </p>
            <p>本服务不受干扰，及时、安全、可靠或不出现错误；</p>
            <p>用户经由本服务取得的任何产品、服务或其他材料符合用户的期望；</p>
            <p>16.4 用户使用经由本服务下载的或取得的任何资料，其风险自行负担；因该使用而导致用户手机或其他设备系统损坏或资料流失，用户应负完全责任；</p>
            <p>16.5 对基于以下原因而造成的利润、商业信誉、资料的损失或其他有形或无形损失，立早网络不承担任何直接、间接、附带、衍生或惩罚性的赔偿：</p>
            <p>本服务使用或无法使用； </p>
            <p>经由本服务购买或取得的任何产品、资料或服务； </p>
            <p>用户资料遭到未授权的使用或修改； </p>
            <p>用户资料丢失或被删除； </p>
            <p>其他与本服务相关的事宜。 </p>
            <p>16.6 用户在浏览网际网路时自行判断使用账号的检索目录。该检索目录可能会引导用户进入到被认为具有攻击性或不适当的网站，立早网络没有义务查看检索目录所列网站的内容，因此，对其正确性、合法性、正当性不负任何责任。</p>
            <p>17. 服务的修改和终止 </p>
            <p>立早网络有权在未事前通知的情况下在任何时候，暂时或永久地修改或终止本服务或其中任何一部分。立早网络对本服务的修改或终止对用户和任何第三人不承担任何责任。立早网络有权基于任何理由，终止用户的账号、密码或拒绝其使用本服务，或删除、转移用户存储、发布在本服务的内容，立早网络采取上述行为均不需通知，并且对用户和任何第三人不承担任何责任。 </p>
            <p>18.用户申请开具发票时间限制 </p>
            <p>用户如需立早网络开具发票，需在交易行为发生之日起一个月内向立早网络提出需求，如用户未能在上述期限内向立早网络提出开具发票的需求，立早网络有权拒绝向用户开具发票。 </p>
            <p>19. 通知 </p>
            <p>立早网络向用户发出的通知，可以采用电子邮件、页面公告、常规信件、电话或立早网络认为适合的形式。服务条款的修改或其他事项变更时，立早网络将会以上述形式进行通知。</p>
            <p>20. 全部协议 </p>
            <p>本服务协议和立早网络的其他服务条款构成完整的协议。 </p>
            <p>21. 法律的适用和管辖</p>
            <p>本服务条款的生效、履行、解释及争议的解决均适用中华人民共和国法律，发生的争议提交公司所在地人民法院裁决。如果本服务协议中某项条款因与中华人民共和国现行法律相抵触而导致无效，将不影响其他部分的效力。</p>
            <p>22. 生效条件</p>
            <p>本协议自用户访问、浏览及使用账号之时开始生效。 </p>
            <h3>二、用户信息保密协议  </h3>
            <p>欢迎您使用立早网络提供的服务，本隐私协议旨在协助您了解您在使用我们提供的服务的过程中，我们将会收集哪些信息，为什么收集该些信息以及您如何查看并删除该些信息。 </p>
            <p>注意：若您为未满16周岁的未成年人，请您在使用我们为您提供的服务前，务必与您的监护人（法定代理人）共同阅读该隐私协议，并征得您的监护人（法定代理人）的同意。 </p>
            <p>1、我们将会为您提供的服务，包括但不限于： </p>
            <p>（1）创建账号； </p>
            <p>（2）游戏娱乐体验； </p>
            <p>（3）充值服务；  </p>
            <p>2、我们收集信息，是为了给您提供更好的服务。我们希望您了解，我们会在您使用立早网络提供的游戏相关服务时收集哪些信息，为什么需要收集相关信息： </p>
            <p>（1）创建账号时，您需要给我们提供个人信息，包括：用户名、密码、真实姓名、身份证号码、邮箱账号，其中真实姓名和身份证号码用作实名认证，对用户资料真实性进行验证审核，邮箱帐号用作找回密码。  </p>
            <p>（2）我们会在您使用游戏服务过程中收集的信息，包括：账号（若您是通过第三方账号登录注册，将会获取第三方账号信息）、设备ID、IP地址、邮箱账号、地理位置、手机号码，该些信息用于信息给您提供个性化服务，关联您账号内的物品信息，状态，为您提供账号的绑定、找回等账号安全服务，提供活动服务等。我们会在您同意后，才会进行上述相关信息的收集。 </p>
            <p> 3、我们基于为您提供合同约定的服务的目的进行上述信息的收集。您有权利不同意我们收集上述相关信息，但这将会导致我们无法向您提供完整的、更好的服务。 </p>
            <p> 4、您有权查看和删除您的信息： </p>
            <p> （1）您有权利通过联系官方客服（13178806403）的方式，联系我们查看我们已经收集的相关信息。若您认为我们收集的信息有误，您可以提出需求，更正信息。 </p>
            <p> （2）您有权利通过联系官方客服（13178806403）的方式，进行信息的删除。在信息删除后，我们将不会保留相关信息，因此，在下一次您在使用相关服务时候，需要重新授权我们收集相关信息，以便于您使用我们提供的服务。 </p>
            <p>为方便您使用我们提供的服务，您已经提供的信息，在您未删除的情况下，我们将会持续保存。 </p>
            <p> 5、我们在服务内内置了安全功能，以保护您的信息。 </p>
            <p> （1）使用加密技术，以保证您的信息在传输过程中保持私密性；  </p>
            <p>（2）审查我们在收集、储存和处理信息方面的做法，以防未经授权的人员访问我们的系统； </p>
            <p>（3）只允许需要个人信息以对其进行处理的员工访问个人信息，任何拥有此权限的人员需要尊重合同中规定的严格保密义务，否则可能会被处分。  </p>
            <p>5、立早网络可能会与第三方合作向用户提供相关的网络服务，在此情况下，如该第三方同意承担与立早网络同等的保护用户隐私的责任，则立早网络可能将用户的个人信息等提供给该第三方。 </p>
            <p>6、在不透露用户个人隐私资料的前提下，立早网络有权对整个用户数据库进行技术分析并对已进行分析、整理后的用户数据库进行商业上的利用（包括但不限于公布、分析或以其它方式使用用户访问量、访问时段、用户偏好等用户数据信息）。</p>
            <p>7、尽管立早网络对用户的隐私权保护做了极大的努力，但是仍然不能保证现有的安全技术措施使用户的技术信息等不受任何形式的损失。故用户在此同意并确认，任何由于计算机系统问题、因政府管制而造成的暂时性关闭等影响网络正常浏览的不可抗力而造成的用户个人信息泄露、丢失、被盗用或被篡改等，立早网络均无需承担责任。  </p>
            <p>8、立早网络保留随时修改本条款的权利，修改本条款时，立早网络将于相关页面公告修改的事实，而不另行对用户进行个别通知。若用户不同意修改的内容，可停止使用立早网络所提供的服务。若用户继续使用立早网络所提供的任意服务，即视为用户业已接受修订的内容。 </p>
            <p>9、如果用户认为立早网络的个人信息处理行为损害了用户的合法权益，用户可以依《用户注册服务协议》约定方式处理。   </p>
            <p>望您仔细阅读上述保密协议内容，并确定是否同意我们根据上述协议内容获取相关信息并提供服务。如您使用立早网络提供的服务，则视为您对上述协议内容确认了解并同意。如果您对本协议有任何疑问、意见或建议，您可以通过游戏客服与我们联系。</p>

            <br>
            <br>
            <br>

        </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn" onclick="agreeAndClose()">我已阅读并同意</button>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script>
        // 优化滑动验证码实现
        const slider = document.querySelector('.slider-button');
        const bg = document.querySelector('.slider-bg');
        const text = document.querySelector('.slider-text');
        const container = document.querySelector('.slider-container');
        let isMouseDown = false;
        let startX;
        let sliderLeft;
        let isVerified = false;

        function handleStart(event) {
            if (isVerified) return;
            const e = event.type.startsWith('mouse') ? event : event.touches[0];
            isMouseDown = true;
            startX = e.clientX;
            sliderLeft = slider.offsetLeft;
            
            // 添加过渡类
            slider.style.transition = 'none';
            bg.style.transition = 'none';
        }

        function handleMove(event) {
            if (!isMouseDown || isVerified) return;
            event.preventDefault();
            const e = event.type.startsWith('mouse') ? event : event.touches[0];
            const walk = e.clientX - startX;
            let newLeft = sliderLeft + walk;
            
            // 限制滑块移动范围
            const maxLeft = container.clientWidth - slider.clientWidth;
            newLeft = Math.max(0, Math.min(maxLeft, newLeft));
            
            // 更新滑块和背景位置
            requestAnimationFrame(() => {
                slider.style.left = `${newLeft}px`;
                bg.style.width = `${newLeft + slider.clientWidth}px`;
            });
            
            // 验证是否滑到终点
            if (newLeft >= maxLeft - 2) {
                verifySuccess();
            }
        }

        function handleEnd() {
            if (!isVerified && isMouseDown) {
                // 恢复过渡效果
                slider.style.transition = 'left 0.2s ease';
                bg.style.transition = 'width 0.2s ease';
                resetSlider();
            }
            isMouseDown = false;
        }

        function verifySuccess() {
            isVerified = true;
            text.textContent = '验证通过';
            text.style.color = '#52c41a';
            slider.style.backgroundColor = '#52c41a';
            slider.style.transition = 'all 0.3s ease';
            bg.style.transition = 'all 0.3s ease';
            
            const maxLeft = container.clientWidth - slider.clientWidth;
            slider.style.left = `${maxLeft}px`;
            bg.style.width = '100%';
            
            // 移除事件监听
            removeEventListeners();
        }

        function resetSlider() {
            requestAnimationFrame(() => {
                slider.style.left = '0px';
                bg.style.width = '0px';
            });
        }

        function removeEventListeners() {
            slider.removeEventListener('mousedown', handleStart);
            document.removeEventListener('mousemove', handleMove);
            slider.removeEventListener('touchstart', handleStart);
            document.removeEventListener('touchmove', handleMove);
        }

        // 添加事件监听
        slider.addEventListener('mousedown', handleStart);
        document.addEventListener('mousemove', handleMove, { passive: false });
        document.addEventListener('mouseup', handleEnd);
        
        slider.addEventListener('touchstart', handleStart, { passive: false });
        document.addEventListener('touchmove', handleMove, { passive: false });
        document.addEventListener('touchend', handleEnd);
        document.addEventListener('touchcancel', handleEnd);

        // 添加密码验证函数
        function validatePassword() {
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirmPassword');
            const passwordError = document.getElementById('passwordError');
            const registerButton = document.querySelector('.register-button');

            if (confirmPassword.value && password.value !== confirmPassword.value) {
                passwordError.style.display = 'block';
                confirmPassword.style.borderColor = '#ff4d4f';
                registerButton.disabled = true;
                registerButton.style.backgroundColor = '#ccc';
                return false;
            } else {
                passwordError.style.display = 'none';
                confirmPassword.style.borderColor = '#ddd';
                registerButton.disabled = false;
                registerButton.style.backgroundColor = '#2196F3';
                return true;
            }
        }

        // 修改表单提交验证
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!validatePassword()) {
                return;
            }

            if (!isVerified) {
                alert('请完成滑动验证');
                return;
            }

            // 检查是否同意协议
            const agreeTerms = document.getElementById('agreeTerms');
            if (!agreeTerms.checked) {
                alert('请先阅读并同意《账号注册协议》');
                return;
            }

            var formData = new FormData(this);
            var body = new URLSearchParams(formData).toString();
            fetch('index.php?action=register',{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: body
            }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('获取的数据:', data);
                    if (data.success) {
                        localStorage.setItem('isLoggedIn', 'true');
                        window.location.href = 'index.html';
                    } else {
                        alert(data.message)
                    }
                })
                .catch(error => {
                    console.error('获取数据时出错:', error);
                });
        });

        // 模态框相关函数
        function showTermsModal() {
            document.getElementById('termsModal').style.display = 'block';
        }

        function closeTermsModal() {
            document.getElementById('termsModal').style.display = 'none';
        }

        function agreeAndClose() {
            document.getElementById('agreeTerms').checked = true;
            closeTermsModal();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('termsModal');
            if (event.target === modal) {
                closeTermsModal();
            }
        }
    </script>
    <script src="js/common.js"></script>
</body>
</html>