<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布信息</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        /* 发布页面特有样式 */
        .hero {
            padding: 140px 0 80px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 3rem;
            margin-bottom: 16px;
        }

        .hero p {
            font-size: 1.125rem;
            margin-bottom: 40px;
            max-width: 480px;
        }

        /* 发布表单样式 */
        .publish-form {
            max-width: 900px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .publish-title {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #7877c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.01em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 1rem;
            letter-spacing: 0.5px;
        }

        .form-group label .required {
            color: #ff4757;
            margin-left: 4px;
        }

        .form-group input[type="text"],
        .form-group input[type="email"],
        .form-group input[type="tel"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 16px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.05);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-group textarea {
            height: 150px;
            resize: vertical;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #ff7730;
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(255, 119, 48, 0.1);
            transform: translateY(-1px);
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 25px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .upload-btn:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: #ff7730;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 119, 48, 0.2);
            color: white;
        }

        .submit-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
            margin-top: 20px;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(255, 119, 48, 0.4);
        }



        /* 文件预览样式 */
        .file-preview-container {
            margin-top: 15px;
        }

        .file-preview-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-top: 10px;
            backdrop-filter: blur(10px);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .file-icon {
            color: #ff7730;
            font-size: 20px;
        }

        .file-name {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 14px;
            word-break: break-all;
        }

        .remove-file {
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 10px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .remove-file:hover {
            background: #ff3742;
            transform: translateY(-1px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 120px 0 60px;
            }

            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .publish-form {
                padding: 0 16px;
            }

            .publish-title {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2rem;
            }

            .publish-title {
                font-size: 1.6rem;
            }

            .publish-form {
                padding: 0 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="hero">
        <div class="container">
            <h2>发布信息</h2>
            <p>分享您的游戏作品，让更多人发现精彩</p>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="publish-form">
                <!-- <h2 class="publish-title">发布信息</h2> -->
            <form id="publishForm">
                <div class="form-group">
                    <label for="infoType">厂家名称<span class="required">*</span></label>
                    <input type="text" id="infoType" name="infoType" required placeholder="请输入厂家名称">
                </div>

                <div class="form-group">
                    <label for="infoTitle">详细地址<span class="required">*</span></label>
                    <input type="text" id="infoTitle" name="infoTitle" required placeholder="请输入详细地址">
                </div>

                <div class="form-group">
                    <label for="phone">联系电话<span class="required">*</span></label>
                    <input type="tel" id="phone" name="phone" required placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <label for="infoDesc">游戏简介<span class="required">*</span></label>
                    <textarea id="infoDesc" name="infoDesc" required placeholder="请输入游戏简介..."></textarea>
                </div>

                <div class="form-group">
                    <label for="infoImage">上传文件<span class="required">*</span></label>
                    <input type="file" id="infoImage" name="infoImage" style="display: none;">
                    <button type="button" class="upload-btn" onclick="document.getElementById('infoImage').click()">
                        <i class="fas fa-upload"></i> 选取文件
                    </button>
                    <div class="file-preview-container" id="filePreviewContainer"></div>
                </div>

                <button type="submit" class="submit-btn">
                    <i class="fas fa-paper-plane"></i> 提交发布
                </button>
            </form>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script>
        // if (localStorage.getItem('isLoggedIn') != 'true') {
        //     alert('请先登录')
        //     window.location.href = 'login.html'
        // }

        // 处理文件预览
        document.getElementById('infoImage').addEventListener('change', function(e) {
            const container = document.getElementById('filePreviewContainer');
            const file = e.target.files[0];

            // 清除之前的文件显示
            container.innerHTML = '';

            if (file) {
                // 获取文件图标
                const getFileIcon = (fileName) => {
                    const extension = fileName.split('.').pop().toLowerCase();
                    const iconMap = {
                        'pdf': 'fas fa-file-pdf',
                        'doc': 'fas fa-file-word',
                        'docx': 'fas fa-file-word',
                        'xls': 'fas fa-file-excel',
                        'xlsx': 'fas fa-file-excel',
                        'ppt': 'fas fa-file-powerpoint',
                        'pptx': 'fas fa-file-powerpoint',
                        'txt': 'fas fa-file-alt',
                        'zip': 'fas fa-file-archive',
                        'rar': 'fas fa-file-archive',
                        'jpg': 'fas fa-file-image',
                        'jpeg': 'fas fa-file-image',
                        'png': 'fas fa-file-image',
                        'gif': 'fas fa-file-image',
                        'mp4': 'fas fa-file-video',
                        'avi': 'fas fa-file-video',
                        'mp3': 'fas fa-file-audio',
                        'wav': 'fas fa-file-audio'
                    };
                    return iconMap[extension] || 'fas fa-file';
                };

                const fileItem = document.createElement('div');
                fileItem.className = 'file-preview-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <i class="${getFileIcon(file.name)} file-icon"></i>
                        <span class="file-name">${file.name}</span>
                    </div>
                    <button type="button" class="remove-file" onclick="removeFile()">删除</button>
                `;
                container.appendChild(fileItem);
            }
        });

        // 删除文件函数
        function removeFile() {
            document.getElementById('infoImage').value = '';
            document.getElementById('filePreviewContainer').innerHTML = '';
        }

        // 修改表单提交处理
        document.getElementById('publishForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 检查是否上传了文件
            const fileInput = document.getElementById('infoImage');
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('请上传一个文件！');
                return false;
            }

            // 检查其他必填字段
            // const requiredFields = ['infoType', 'infoTitle', 'infoDesc', 'companyName', 'contact', 'phone', 'email', 'address'];
            // for (let fieldId of requiredFields) {
            //     const field = document.getElementById(fieldId);
            //     if (!field.value.trim()) {
            //         alert(`请填写${field.previousElementSibling.textContent.replace(' *', '')}`);
            //         field.focus();
            //         return false;
            //     }
            // }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(document.getElementById('phone').value)) {
                alert('请输入正确的手机号码');
                document.getElementById('phone').focus();
                return false;
            }

            // 验证邮箱格式
            // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            // if (!emailRegex.test(document.getElementById('email').value)) {
            //     alert('请输入正确的邮箱地址');
            //     document.getElementById('email').focus();
            //     return false;
            // }

            // console.log('文件数据:', fileInput.files[0]);

            var formData = new FormData(this);
            var body = new URLSearchParams(formData).toString();
            fetch('index.php?action=publish',{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: body
            }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('获取的数据:', data);
                    alert('信息已提交，请耐心等候后台审核');
                    location.reload();
                })
                .catch(error => {
                    console.error('获取数据时出错:', error);
                });
        });
    </script>
    <script src="js/common.js"></script>
</body>
</html>